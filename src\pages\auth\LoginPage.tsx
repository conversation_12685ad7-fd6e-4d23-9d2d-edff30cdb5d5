import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Eye, EyeOff, Loader2, ArrowLeft, GraduationCap, Users } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { useAuth } from '../../contexts/AuthContext';

// Validation interfaces
interface ValidationState {
  isValid: boolean;
  message: string;
}

interface LoginValidation {
  email: ValidationState;
  password: ValidationState;
}

interface StudentValidation {
  email: ValidationState;
  password: ValidationState;
}

const LoginPage = () => {
  const [loginType, setLoginType] = useState<'parent' | 'student'>('parent');
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { login, studentLogin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination or default to dashboard
  const from = location.state?.from?.pathname || '/dashboard';

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      let success = false;
      if (loginType === 'parent') {
        success = await login(formData.email, formData.password);
      } else {
        success = await studentLogin(formData.email, formData.password);
      }

      if (success) {
        navigate(from, { replace: true });
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-8 lg:p-12">
        <div className="w-full max-w-lg">
          {/* Back Button */}
          <div className="mb-6">
            <Link
              to="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-10">
            <h1 className="text-5xl font-bold text-gray-900 mb-4 leading-tight">
              Welcome back!
            </h1>
            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              Simplify your workflow and boost your productivity with Zoffness College Prep. Get started for free.
            </p>

            {/* Login Type Toggle */}
            <div className="flex items-center justify-center mb-8">
              <div className="bg-gray-100 rounded-2xl p-1 flex">
                <button
                  type="button"
                  onClick={() => setLoginType('parent')}
                  className={`flex items-center px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ${
                    loginType === 'parent'
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <Users className="w-4 h-4 mr-2" />
                  Parent Login
                </button>
                <button
                  type="button"
                  onClick={() => setLoginType('student')}
                  className={`flex items-center px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ${
                    loginType === 'student'
                      ? 'bg-white text-green-600 shadow-md'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <GraduationCap className="w-4 h-4 mr-2" />
                  Student Login
                </button>
              </div>
            </div>
          </div>

          {/* Form Container */}
          <div className="transition-all duration-500 transform">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder={loginType === 'parent' ? 'Parent Email' : 'Student Email'}
                  required
                  className={`h-14 rounded-2xl border-2 px-6 text-lg transition-all duration-300 ${
                    loginType === 'parent'
                      ? 'border-gray-200 focus:border-blue-500 hover:border-gray-300 focus:shadow-lg focus:shadow-blue-100/50'
                      : 'border-gray-200 focus:border-green-500 hover:border-gray-300 focus:shadow-lg focus:shadow-green-100/50'
                  }`}
                />
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder={loginType === 'parent' ? 'Parent Password' : 'Student Password'}
                    required
                    className={`h-14 rounded-2xl border-2 px-6 pr-14 text-lg transition-all duration-300 ${
                      loginType === 'parent'
                        ? 'border-gray-200 focus:border-blue-500 hover:border-gray-300 focus:shadow-lg focus:shadow-blue-100/50'
                        : 'border-gray-200 focus:border-green-500 hover:border-gray-300 focus:shadow-lg focus:shadow-green-100/50'
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-5 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors z-10"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              {/* Forgot Password Link */}
              <div className="text-right">
                <Link
                  to="/auth/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium"
                >
                  Forgot Password?
                </Link>
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-16 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                      Signing In...
                    </>
                  ) : (
                    <>
                      Login as Parent
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>

          {/* Sign Up Link */}
          <div className="mt-8 text-center">
            <p className="text-lg text-gray-600">
              Not a member?{' '}
              <Link
                to="/auth/signup"
                className="text-blue-600 font-semibold hover:text-blue-800 hover:underline transition-colors"
              >
                Register now
              </Link>
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Image */}
      <div className="flex-1 bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-8">
        <div className="text-center max-w-lg">
          {/* Student Image */}
          <div className="mb-8">
            <div className="relative mx-auto">
              <img
                src="/pexels-emily-ranquist-493228-1205651.jpg"
                alt="Student learning with Zoffness College Prep"
                className="w-full max-w-md mx-auto rounded-2xl shadow-2xl object-cover"
              />
              {/* Overlay gradient for better text readability */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
            </div>
          </div>

          {/* Text */}
          <h2 className="text-3xl font-bold text-gray-900 mb-4 leading-tight">
            Make your work easier and organized with Zoffness College Prep
          </h2>
          <p className="text-lg text-gray-600">
            Welcome back! Continue your learning journey with personalized study plans and expert guidance.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
