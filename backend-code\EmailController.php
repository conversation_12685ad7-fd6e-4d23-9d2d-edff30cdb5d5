<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EmailController extends Controller
{
    /**
     * Send email notification
     */
    public function sendEmail(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'to' => 'required|email',
            'subject' => 'required|string|max:255',
            'html' => 'required|string',
            'type' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Log the email attempt
            Log::info('Sending email notification', [
                'to' => $request->to,
                'subject' => $request->subject,
                'type' => $request->type
            ]);

            // Send the email
            Mail::send([], [], function ($message) use ($request) {
                $message->to($request->to)
                        ->subject($request->subject)
                        ->html($request->html)
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Log success
            Log::info('Email sent successfully', [
                'to' => $request->to,
                'type' => $request->type
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Email sent successfully',
                'email_id' => 'email_' . time() . '_' . uniqid()
            ]);

        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to send email', [
                'to' => $request->to,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to send email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $testEmail = $request->input('email', '<EMAIL>');

        try {
            Mail::send([], [], function ($message) use ($testEmail) {
                $message->to($testEmail)
                        ->subject('Zoffness Academy - Email Test')
                        ->html('<h1>Email Test Successful!</h1><p>Your email configuration is working correctly.</p>')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully to ' . $testEmail
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Test email failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
