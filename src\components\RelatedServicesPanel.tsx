import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useServiceSelection } from '../contexts/ServiceSelectionContext';

interface RelatedService {
  id: string;
  title: string;
  description: string;
  icon: string;
  formPath: string;
  serviceType: string;
}

interface RelatedServicesPanelProps {
  currentServiceType: string;
  onServiceSelect?: (serviceType: string) => void;
}

const RelatedServicesPanel: React.FC<RelatedServicesPanelProps> = ({ 
  currentServiceType, 
  onServiceSelect 
}) => {
  const navigate = useNavigate();
  const { hasService, selectedServices, totalAmount } = useServiceSelection();

  // Define all related services
  const allServices: RelatedService[] = [
    {
      id: 'sat-act-diagnostic',
      title: 'SAT/ACT Diagnostic Test',
      description: 'Pinpoint your strengths and weaknesses',
      icon: '🎯',
      formPath: '/forms/sat-act-diagnostic',
      serviceType: 'sat-act-diagnostic'
    },
    {
      id: 'sat-act-course',
      title: 'SAT/ACT Course Registration',
      description: 'Comprehensive test preparation',
      icon: '📚',
      formPath: '/forms/sat-act-course',
      serviceType: 'sat-act-course'
    },
    {
      id: 'sat-act-practice-test',
      title: 'SAT/ACT Practice Test Registration',
      description: 'Practice with real test conditions',
      icon: '📝',
      formPath: '/forms/sat-act-practice-test',
      serviceType: 'sat-act-practice-test'
    },
    {
      id: 'college-admissions',
      title: 'College Admissions Counseling',
      description: 'Expert guidance through admissions',
      icon: '🎓',
      formPath: '/forms/college-admissions',
      serviceType: 'college-admissions'
    },
    {
      id: 'college-essays',
      title: 'College Essays',
      description: 'Expert essay writing support',
      icon: '✍️',
      formPath: '/forms/college-essays',
      serviceType: 'college-essays'
    },
    {
      id: 'executive-function',
      title: 'Executive Function',
      description: 'Develop essential skills',
      icon: '🧠',
      formPath: '/forms/executive-function',
      serviceType: 'executive-function'
    }
  ];

  // Filter out the current service and get related services
  const getRelatedServices = () => {
    if (currentServiceType.includes('sat-act')) {
      // For SAT/ACT services, show other SAT/ACT services
      return allServices.filter(service => 
        service.serviceType.includes('sat-act') && service.serviceType !== currentServiceType
      );
    } else {
      // For other services, show all services except current
      return allServices.filter(service => service.serviceType !== currentServiceType);
    }
  };

  const relatedServices = getRelatedServices();

  const handleServiceClick = (service: RelatedService) => {
    if (onServiceSelect) {
      onServiceSelect(service.serviceType);
    } else {
      navigate(service.formPath);
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Selections Summary */}
      {selectedServices.length > 0 && (
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-5 rounded-xl border border-green-200 shadow-sm">
          <h4 className="font-semibold text-green-900 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Selected Services
          </h4>
          <div className="space-y-2 mb-4">
            {selectedServices.map((service, index) => (
              <div key={index} className="flex justify-between items-center text-sm">
                <span className="text-green-800">{service.packageName}</span>
                <span className="font-medium text-green-900">${service.price}</span>
              </div>
            ))}
          </div>
          <div className="border-t border-green-200 pt-3">
            <div className="flex justify-between items-center font-bold text-green-900">
              <span>Total Amount:</span>
              <span className="text-lg">${totalAmount}</span>
            </div>
          </div>
        </div>
      )}

      {/* Related Services */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-5 rounded-xl border border-blue-200 shadow-sm">
        <h4 className="font-semibold text-blue-900 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          {currentServiceType.includes('sat-act') ? 'Related SAT/ACT Services' : 'Related Services'}
        </h4>
        <div className="space-y-3">
          {relatedServices.map((service) => {
            const isSelected = hasService(service.serviceType);
            return (
              <button
                key={service.id}
                onClick={() => handleServiceClick(service)}
                className={`group w-full flex items-start p-3 rounded-lg border transition-all duration-200 hover:scale-[1.02] text-left ${
                  isSelected 
                    ? 'bg-blue-100 border-blue-300 shadow-md' 
                    : 'bg-white border-blue-100 hover:border-blue-300 hover:shadow-md'
                }`}
              >
                <div className={`flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors ${
                  isSelected 
                    ? 'bg-blue-200' 
                    : 'bg-blue-100 group-hover:bg-blue-200'
                }`}>
                  <span className="text-lg">{service.icon}</span>
                </div>
                <div className="flex-1">
                  <div className={`font-medium transition-colors ${
                    isSelected 
                      ? 'text-blue-800' 
                      : 'text-blue-900 group-hover:text-blue-700'
                  }`}>
                    {service.title}
                    {isSelected && (
                      <span className="ml-2 text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        Selected
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {service.description}
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Add More Services */}
      <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-5 rounded-xl border border-purple-200 shadow-sm">
        <h4 className="font-semibold text-purple-900 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add More Services
        </h4>
        <p className="text-sm text-purple-700 mb-3">
          Combine multiple services for a comprehensive educational experience and save on total costs.
        </p>
        <button
          onClick={() => navigate('/service-selection')}
          className="w-full bg-purple-100 hover:bg-purple-200 text-purple-800 font-medium py-2 px-4 rounded-lg transition-colors"
        >
          View All Services
        </button>
      </div>
    </div>
  );
};

export default RelatedServicesPanel;
