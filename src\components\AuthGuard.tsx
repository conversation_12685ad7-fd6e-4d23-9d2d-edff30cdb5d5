import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Lock, UserPlus, LogIn, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AuthGuardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  showLoginOptions?: boolean;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  title = "Authentication Required",
  description = "Please log in or create an account to access this service form.",
  showLoginOptions = true
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // If user is authenticated, show the protected content
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // If not authenticated, show the authentication required screen
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
              {title}
            </CardTitle>
            <CardDescription className="text-gray-600 text-base leading-relaxed">
              {description}
            </CardDescription>
          </CardHeader>

          {showLoginOptions && (
            <CardContent className="space-y-4">
              {/* Login Button */}
              <Button
                onClick={() => navigate('/auth/login')}
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <LogIn className="mr-2 h-5 w-5" />
                Log In to Your Account
              </Button>

              {/* Signup Button */}
              <Button
                onClick={() => navigate('/auth/signup')}
                variant="outline"
                className="w-full h-12 border-2 border-blue-200 text-blue-600 hover:bg-blue-50 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
              >
                <UserPlus className="mr-2 h-5 w-5" />
                Create New Account
              </Button>

              {/* Divider */}
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-gray-200" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-white text-gray-500 font-medium">
                    Why do I need to log in?
                  </span>
                </div>
              </div>

              {/* Benefits */}
              <div className="bg-blue-50 rounded-xl p-4 space-y-3">
                <div className="flex items-start space-x-3">
                  <Lock className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Secure & Personalized</p>
                    <p className="text-xs text-blue-700">Your information is protected and forms are saved to your account</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Track Your Services</p>
                    <p className="text-xs text-blue-700">Manage all your educational services in one place</p>
                  </div>
                </div>
              </div>

              {/* Back to Home */}
              <div className="text-center pt-4">
                <Button
                  onClick={() => navigate('/')}
                  variant="ghost"
                  className="text-gray-500 hover:text-gray-700 text-sm"
                >
                  ← Back to Home
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
};

export default AuthGuard;
