import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { useToast } from '../components/ui/use-toast';
import { showEmailAlreadyRegisteredToast, showUsernameUnavailableToast, showPasswordRequirementsToast } from '@/components/ui/CustomToast';

// API Configuration
const API_ENDPOINTS = {
  SIGNUP: 'https://zoffness.academy/api/register',
  SIGNIN: 'https://zoffness.academy/api/login',
  VERIFY: 'https://zoffness.academy/api/verify',
  STUDENTS: 'https://zoffness.academy/api/students'
};

// Define user types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  userType: 'parent' | 'student';
  phone?: string;
  createdAt: string;
  parentId?: string;
  students?: Student[];
}

export interface StudentCreationData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  grade?: string;
  school?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (userData: SignupData) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  createStudent: (studentData: StudentCreationData) => Promise<Student | null>;
  getStudents: () => Student[];
  updateStudent: (studentId: string, studentData: Partial<Student>) => Promise<boolean>;
  deleteStudent: (studentId: string) => Promise<boolean>;
  studentLogin: (email: string, password: string) => Promise<boolean>;
}

export interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  userType: 'parent';
  phone?: string;
  username?: string;
}

export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  parentId: string;
  createdAt: string;
  grade?: string;
  school?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (token) {
          // For now, create a mock user since backend verification might not be ready
          // TODO: Replace with actual API verification when backend is ready
          const mockUser: User = {
            id: 'user_' + Date.now(),
            email: '<EMAIL>',
            firstName: 'Parent',
            lastName: 'User',
            userType: 'parent',
            createdAt: new Date().toISOString(),
            students: []
          };

          // Load students from localStorage
          const storedStudents = localStorage.getItem('userStudents_' + mockUser.id);
          if (storedStudents) {
            try {
              mockUser.students = JSON.parse(storedStudents);
            } catch (error) {
              console.error('Error parsing stored students:', error);
              mockUser.students = [];
            }
          }

          setUser(mockUser);

          /*
          // Uncomment this when backend verification is ready:
          const response = await axios.get(API_ENDPOINTS.VERIFY, {
            headers: { Authorization: `Bearer ${token}` }
          });

          if (response.data.success) {
            const userData = response.data.user;
            // Load students from localStorage
            const storedStudents = localStorage.getItem('userStudents_' + userData.id);
            if (storedStudents) {
              try {
                userData.students = JSON.parse(storedStudents);
              } catch (error) {
                console.error('Error parsing stored students:', error);
                userData.students = [];
              }
            }
            setUser(userData);
          } else {
            localStorage.removeItem('authToken');
          }
          */
        }
      } catch (error) {
        console.error('Auth verification failed:', error);
        localStorage.removeItem('authToken');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const response = await axios.post(API_ENDPOINTS.SIGNIN, {
        email,
        password
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.data.success || response.status === 200 || response.status === 201) {
        const userData = response.data.user || response.data.data || response.data;
        const token = response.data.token || response.data.access_token || 'api-token-' + Date.now();

        // Load students from localStorage
        const storedStudents = localStorage.getItem('userStudents_' + userData.id);
        if (storedStudents) {
          try {
            userData.students = JSON.parse(storedStudents);
          } catch (error) {
            console.error('Error parsing stored students:', error);
            userData.students = [];
          }
        }

        setUser({
          ...userData,
          userType: userData.userType || userData.user_type || 'parent'
        });
        localStorage.setItem('authToken', token);

        toast({
          title: 'Login Successful',
          description: `Welcome back, ${userData.firstName || userData.first_name}!`,
        });

        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Login error:', error);
      
      let errorMessage = error.response?.data?.message ||
                       error.response?.data?.error ||
                       'Invalid email or password. Please try again.';

      if (error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        const errorFields = Object.keys(validationErrors);

        if (errorFields.length > 0) {
          if (validationErrors.email) {
            const emailError = Array.isArray(validationErrors.email) ? validationErrors.email[0] : validationErrors.email;
            errorMessage = emailError.includes('required') ? 'Please enter your email address.' :
                          emailError.includes('email') ? 'Please enter a valid email address.' :
                          emailError;
          } else if (validationErrors.password) {
            const passwordError = Array.isArray(validationErrors.password) ? validationErrors.password[0] : validationErrors.password;
            errorMessage = passwordError.includes('required') ? 'Please enter your password.' : passwordError;
          } else {
            const firstError = validationErrors[errorFields[0]];
            errorMessage = `Please check your ${errorFields[0]}: ${Array.isArray(firstError) ? firstError[0] : firstError}`;
          }
        }
      }

      toast({
        title: 'Login Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData: SignupData): Promise<boolean> => {
    try {
      setIsLoading(true);

      if (!userData.firstName || !userData.lastName || !userData.email || !userData.password) {
        toast({
          title: 'Signup Failed',
          description: 'Please fill in all required fields.',
          variant: 'destructive',
        });
        return false;
      }

      if (userData.password !== userData.confirmPassword) {
        toast({
          title: 'Signup Failed',
          description: 'Passwords do not match.',
          variant: 'destructive',
        });
        return false;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        toast({
          title: 'Signup Failed',
          description: 'Please enter a valid email address.',
          variant: 'destructive',
        });
        return false;
      }

      if (userData.password.length < 8) {
        toast({
          title: 'Signup Failed',
          description: 'Password must be at least 8 characters long.',
          variant: 'destructive',
        });
        return false;
      }

      const username = userData.username || userData.email.split('@')[0];
      const response = await axios.post(API_ENDPOINTS.SIGNUP, {
        username,
        first_name: userData.firstName,
        last_name: userData.lastName,
        email: userData.email,
        password: userData.password,
        password_confirmation: userData.confirmPassword,
        user_type: userData.userType,
        phone: userData.phone || null
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.data.success || response.status === 200 || response.status === 201) {
        const newUser = response.data.user || response.data.data || response.data;
        const token = response.data.token || response.data.access_token || 'api-token-' + Date.now();

        setUser({
          ...newUser,
          userType: newUser.userType || newUser.user_type || 'parent'
        });
        localStorage.setItem('authToken', token);

        toast({
          title: 'Account Created',
          description: `Welcome to Zoffness, ${newUser.firstName || newUser.first_name}!`,
        });

        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Signup error:', error);
      
      let errorMessage = error.response?.data?.message ||
                       error.response?.data?.error ||
                       'Failed to create account. Please try again.';

      if (error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        if (validationErrors.email) {
          showEmailAlreadyRegisteredToast();
          return false;
        }
        if (validationErrors.username) {
          showUsernameUnavailableToast();
          return false;
        }
        if (validationErrors.password) {
          const passwordError = Array.isArray(validationErrors.password) ? validationErrors.password[0] : validationErrors.password;
          showPasswordRequirementsToast(passwordError);
          return false;
        }
      }

      toast({
        title: 'Signup Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('authToken');
    
    toast({
      title: 'Logged Out',
      description: 'You have been successfully logged out.',
    });
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  const createStudent = async (studentData: StudentCreationData): Promise<Student | null> => {
    if (!user || user.userType !== 'parent') {
      toast({
        title: 'Access Denied',
        description: 'Only parents can create student accounts.',
        variant: 'destructive',
      });
      return null;
    }

    try {
      setIsLoading(true);

      // For now, create student locally until backend API is ready
      // TODO: Replace with actual API call when backend is implemented
      const newStudent: Student = {
        id: 'student_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        firstName: studentData.firstName,
        lastName: studentData.lastName,
        email: studentData.email,
        parentId: user.id,
        createdAt: new Date().toISOString(),
        grade: studentData.grade,
        school: studentData.school
      };

      const updatedStudents = [...(user.students || []), newStudent];
      setUser({ ...user, students: updatedStudents });

      // Store student credentials securely
      const studentAccounts = JSON.parse(localStorage.getItem('studentAccounts') || '[]');
      studentAccounts.push({
        email: studentData.email,
        password: studentData.password,
        student: newStudent
      });
      localStorage.setItem('studentAccounts', JSON.stringify(studentAccounts));

      // Store students in localStorage for persistence
      localStorage.setItem('userStudents_' + user.id, JSON.stringify(updatedStudents));

      toast({
        title: 'Student Created',
        description: `Student account for ${newStudent.firstName} created successfully.`,
      });

      return newStudent;

      /*
      // Uncomment this when backend API is ready:
      const token = localStorage.getItem('authToken');
      const response = await axios.post(API_ENDPOINTS.STUDENTS, {
        ...studentData,
        parentId: user.id
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.data.success || response.status === 200 || response.status === 201) {
        const newStudent = response.data.student || response.data.data;
        const updatedStudents = [...(user.students || []), newStudent];

        setUser({ ...user, students: updatedStudents });

        // Store student credentials securely
        const studentAccounts = JSON.parse(localStorage.getItem('studentAccounts') || '[]');
        studentAccounts.push({
          email: studentData.email,
          password: studentData.password,
          student: newStudent
        });
        localStorage.setItem('studentAccounts', JSON.stringify(studentAccounts));

        toast({
          title: 'Student Created',
          description: `Student account for ${newStudent.firstName} created successfully.`,
        });

        return newStudent;
      }
      return null;
      */
    } catch (error: any) {
      console.error('Create student error:', error);
      toast({
        title: 'Failed to Create Student',
        description: error.response?.data?.message || 'Unable to create student account.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const getStudents = (): Student[] => {
    return user?.students || [];
  };

  const updateStudent = async (studentId: string, studentData: Partial<Student>): Promise<boolean> => {
    if (!user || user.userType !== 'parent') {
      toast({
        title: 'Access Denied',
        description: 'Only parents can update student accounts.',
        variant: 'destructive',
      });
      return false;
    }

    try {
      setIsLoading(true);

      // For now, update student locally until backend API is ready
      const updatedStudents = (user.students || []).map(student =>
        student.id === studentId ? { ...student, ...studentData } : student
      );
      setUser({ ...user, students: updatedStudents });

      // Store updated students in localStorage for persistence
      localStorage.setItem('userStudents_' + user.id, JSON.stringify(updatedStudents));

      toast({
        title: 'Student Updated',
        description: 'Student information has been updated successfully.',
      });
      return true;

      /*
      // Uncomment this when backend API is ready:
      const token = localStorage.getItem('authToken');
      const response = await axios.put(`${API_ENDPOINTS.STUDENTS}/${studentId}`, studentData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.data.success || response.status === 200) {
        const updatedStudents = (user.students || []).map(student =>
          student.id === studentId ? { ...student, ...studentData } : student
        );
        setUser({ ...user, students: updatedStudents });

        toast({
          title: 'Student Updated',
          description: 'Student information has been updated successfully.',
        });
        return true;
      }
      return false;
      */
    } catch (error) {
      console.error('Error updating student:', error);
      toast({
        title: 'Update Failed',
        description: 'Unable to update student information.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteStudent = async (studentId: string): Promise<boolean> => {
    if (!user || user.userType !== 'parent') {
      toast({
        title: 'Access Denied',
        description: 'Only parents can delete student accounts.',
        variant: 'destructive',
      });
      return false;
    }

    try {
      setIsLoading(true);

      // For now, delete student locally until backend API is ready
      const updatedStudents = (user.students || []).filter(student => student.id !== studentId);
      setUser({ ...user, students: updatedStudents });

      // Update localStorage
      localStorage.setItem('userStudents_' + user.id, JSON.stringify(updatedStudents));

      const studentAccounts = JSON.parse(localStorage.getItem('studentAccounts') || '[]');
      const updatedStorageStudents = studentAccounts.filter((account: any) => account.student.id !== studentId);
      localStorage.setItem('studentAccounts', JSON.stringify(updatedStorageStudents));

      toast({
        title: 'Student Removed',
        description: 'Student account has been removed successfully.',
      });
      return true;

      /*
      // Uncomment this when backend API is ready:
      const token = localStorage.getItem('authToken');
      const response = await axios.delete(`${API_ENDPOINTS.STUDENTS}/${studentId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.data.success || response.status === 200) {
        const updatedStudents = (user.students || []).filter(student => student.id !== studentId);
        setUser({ ...user, students: updatedStudents });

        const studentAccounts = JSON.parse(localStorage.getItem('studentAccounts') || '[]');
        const updatedStorageStudents = studentAccounts.filter((account: any) => account.student.id !== studentId);
        localStorage.setItem('studentAccounts', JSON.stringify(updatedStorageStudents));

        toast({
          title: 'Student Removed',
          description: 'Student account has been removed successfully.',
        });
        return true;
      }
      return false;
      */
    } catch (error) {
      console.error('Error deleting student:', error);
      toast({
        title: 'Delete Failed',
        description: 'Unable to remove student account.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const studentLogin = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await axios.post(API_ENDPOINTS.SIGNIN, {
        email,
        password,
        userType: 'student'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.data.success || response.status === 200 || response.status === 201) {
        const studentUser = response.data.user || response.data.data;
        setUser({
          ...studentUser,
          userType: 'student'
        });
        localStorage.setItem('authToken', response.data.token || 'student-token-' + Date.now());

        toast({
          title: 'Student Login Successful',
          description: `Welcome, ${studentUser.firstName}!`,
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Student login error:', error);
      toast({
        title: 'Login Failed',
        description: 'Invalid email or password. Please check your credentials.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    signup,
    logout,
    updateUser,
    createStudent,
    getStudents,
    updateStudent,
    deleteStudent,
    studentLogin
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;