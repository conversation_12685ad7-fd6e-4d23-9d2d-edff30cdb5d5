# Quick Email Setup Instructions

## Step 1: Add the Email Controller

1. Copy `EmailController.php` to your Laravel project:
   ```bash
   cp backend-code/EmailController.php app/Http/Controllers/EmailController.php
   ```

## Step 2: Add Routes

1. Open your `routes/api.php` file
2. Add these lines at the top (if not already there):
   ```php
   use App\Http\Controllers\EmailController;
   ```
3. Add these routes:
   ```php
   Route::post('/send-email', [EmailController::class, 'sendEmail']);
   Route::post('/test-email', [EmailController::class, 'testEmail']);
   ```

## Step 3: Configure Email Settings

1. Open your `.env` file and add/update these settings:

### For Gmail SMTP:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Zoffness Academy"
```

### For Other SMTP Services:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Zoffness Academy"
```

## Step 4: Test the Setup

1. **Test via API:**
   ```bash
   curl -X POST https://zoffness.academy/api/test-email \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'
   ```

2. **Test via Browser Console:**
   - Open your website
   - Open browser console (F12)
   - Run:
   ```javascript
   fetch('https://zoffness.academy/api/test-email', {
     method: 'POST',
     headers: {'Content-Type': 'application/json'},
     body: JSON.stringify({email: '<EMAIL>'})
   }).then(r => r.json()).then(console.log)
   ```

## Step 5: Clear Cache (if needed)

```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
```

## Gmail Setup (if using Gmail)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password:**
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
   - Use this password in MAIL_PASSWORD (not your regular Gmail password)

## Alternative: Quick Test Without Email

If you want to test the student creation without email for now, I can temporarily disable the email feature. Just let me know!

## Troubleshooting

### Common Issues:

1. **"Connection refused"** - Check MAIL_HOST and MAIL_PORT
2. **"Authentication failed"** - Check MAIL_USERNAME and MAIL_PASSWORD
3. **"SSL/TLS error"** - Try changing MAIL_ENCRYPTION to 'ssl' or null

### Debug Steps:

1. Check Laravel logs: `storage/logs/laravel.log`
2. Test with a simple email service like Mailtrap for development
3. Verify your email provider allows SMTP access

## Success!

Once set up, you should see:
- ✅ Student accounts created successfully
- ✅ Email notifications sent automatically
- ✅ Professional HTML emails with login credentials
- ✅ Proper error handling and logging
