<?php

// Add these routes to your routes/api.php file

use App\Http\Controllers\EmailController;

// Email notification routes
Route::post('/send-email', [EmailController::class, 'sendEmail']);
Route::post('/test-email', [EmailController::class, 'testEmail']);

// Example of how your routes/api.php should look:
/*
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\EmailController;

// Existing routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/verify', [AuthController::class, 'verify']);

// New email routes
Route::post('/send-email', [EmailController::class, 'sendEmail']);
Route::post('/test-email', [EmailController::class, 'testEmail']);

// Other routes...
*/
