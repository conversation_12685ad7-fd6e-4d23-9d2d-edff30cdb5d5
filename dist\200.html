<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="/favicon-192.png" />
    <link rel="apple-touch-icon" href="/favicon-192.png" />
    <title>Zoffness College Prep Programs</title>
    <meta name="description" content="Expert College Admissions Counseling" />
    <meta name="author" content="Zoffness" />

    <meta property="og:title" content="Zoffness" />
    <meta property="og:description" content="Expert College Admissions Counseling" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/zoffnesscollegeprep-logo.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@zoffness" />
    <meta name="twitter:image" content="/zoffnesscollegeprep-logo.png" />

    <!-- Handle direct navigation to routes -->
    <script>
      // This script helps with direct navigation to routes
      (function() {
        // Check if this is a direct navigation to a route
        if (window.location.pathname !== '/' && !window.location.pathname.includes('.')) {
          // Store the current path in sessionStorage
          sessionStorage.setItem('redirectPath', window.location.pathname);
        }
      })();
    </script>
    <script type="module" crossorigin src="/assets/index-C-d6YhnO.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-BSQAhbDb.js">
    <link rel="stylesheet" crossorigin href="/assets/index-D0CxgRq2.css">
  </head>

  <body>
    <div id="root"></div>

  </body>
</html>
