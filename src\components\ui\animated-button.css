@keyframes circleExpand {
  0% {
    width: 3rem;
  }
  100% {
    width: 100%;
  }
}

@keyframes arrowMove {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(1rem);
  }
}

.animated-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  outline: none;
  border: 0;
  text-decoration: none;
  background: #6366f1;
  font-size: inherit;
  font-family: inherit;
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.animated-button:hover {
  background: #4f46e5;
}

.button-text {
  color: white;
  font-weight: 600;
  text-align: center;
}

.circle {
  position: absolute;
  left: 0;
  display: block;
  margin: 0;
  width: 3rem;
  height: 100%;
  background: #2563eb;
  border-radius: 1.625rem;
  transition: width 0.45s cubic-bezier(0.65, 0, 0.076, 1);
}

.icon {
  position: absolute;
  left: 0.625rem;
  width: 1.125rem;
  height: 0.125rem;
  background: none;
  transition: transform 0.45s cubic-bezier(0.65, 0, 0.076, 1);
}

.icon::before {
  position: absolute;
  content: '';
  top: -0.25rem;
  right: 0.0625rem;
  width: 0.625rem;
  height: 0.625rem;
  border-top: 0.125rem solid #fff;
  border-right: 0.125rem solid #fff;
  transform: rotate(45deg);
}

.button-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.75rem 0;
  margin: 0 0 0 1.85rem;
  color: #2563eb;
  font-weight: 700;
  line-height: 1.6;
  text-align: center;
  transition: color 0.45s cubic-bezier(0.65, 0, 0.076, 1);
}

.animated-button .circle {
  width: 100%;
}

.animated-button .icon {
  transform: translateX(1rem);
}

.animated-button .button-text {
  color: #fff;
}

.circle, .icon, .icon::before {
  display: none;
}

.animated-button {
  background: linear-gradient(to right, #6366f1, #4338ca);
  color: white;
  padding: 0.75rem 3rem 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: background 0.3s ease;
  height: auto;
  width: auto;
  position: relative;
}

.animated-button::before {
  content: '→';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.animated-button:hover::before {
  transform: translate(0.25rem, -50%);
}

.animated-button:hover {
  background: linear-gradient(to right, #4338ca, #312e81);
}

.button-text {
  position: static;
  margin: 0;
  padding: 0;
  color: inherit;
}