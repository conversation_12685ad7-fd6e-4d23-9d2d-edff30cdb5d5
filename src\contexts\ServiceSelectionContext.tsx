import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define types for service selections
export interface ServicePackage {
  id: string;
  name: string;
  price: number;
  description?: string;
  serviceType: 'sat-act-diagnostic' | 'sat-act-course' | 'sat-act-practice-test' | 'college-admissions' | 'college-essays' | 'executive-function';
}

export interface SelectedService {
  serviceType: string;
  packageId: string;
  packageName: string;
  price: number;
  description?: string;
  formData?: any; // Store specific form data for each service
}

export interface ServiceSelectionContextType {
  selectedServices: SelectedService[];
  totalAmount: number;
  addService: (service: SelectedService) => void;
  removeService: (serviceType: string, packageId: string) => void;
  updateService: (serviceType: string, packageId: string, updates: Partial<SelectedService>) => void;
  clearAllServices: () => void;
  getServicesByType: (serviceType: string) => SelectedService[];
  hasService: (serviceType: string, packageId?: string) => boolean;
  getFormData: (serviceType: string) => any;
  setFormData: (serviceType: string, formData: any) => void;
}

const ServiceSelectionContext = createContext<ServiceSelectionContextType | undefined>(undefined);

export const useServiceSelection = () => {
  const context = useContext(ServiceSelectionContext);
  if (!context) {
    throw new Error('useServiceSelection must be used within a ServiceSelectionProvider');
  }
  return context;
};

interface ServiceSelectionProviderProps {
  children: ReactNode;
}

export const ServiceSelectionProvider: React.FC<ServiceSelectionProviderProps> = ({ children }) => {
  const [selectedServices, setSelectedServices] = useState<SelectedService[]>([]);
  const [formDataStore, setFormDataStore] = useState<{[serviceType: string]: any}>({});

  // Calculate total amount whenever services change
  const totalAmount = selectedServices.reduce((total, service) => total + service.price, 0);

  // Load from localStorage on mount
  useEffect(() => {
    const savedServices = localStorage.getItem('selectedServices');
    const savedFormData = localStorage.getItem('serviceFormData');
    
    if (savedServices) {
      try {
        setSelectedServices(JSON.parse(savedServices));
      } catch (error) {
        console.error('Error loading saved services:', error);
      }
    }
    
    if (savedFormData) {
      try {
        setFormDataStore(JSON.parse(savedFormData));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Save to localStorage whenever services change
  useEffect(() => {
    localStorage.setItem('selectedServices', JSON.stringify(selectedServices));
  }, [selectedServices]);

  // Save form data to localStorage
  useEffect(() => {
    localStorage.setItem('serviceFormData', JSON.stringify(formDataStore));
  }, [formDataStore]);

  const addService = (service: SelectedService) => {
    setSelectedServices(prev => {
      // Remove any existing service of the same type and package
      const filtered = prev.filter(s => !(s.serviceType === service.serviceType && s.packageId === service.packageId));
      return [...filtered, service];
    });
  };

  const removeService = (serviceType: string, packageId: string) => {
    setSelectedServices(prev => 
      prev.filter(s => !(s.serviceType === serviceType && s.packageId === packageId))
    );
  };

  const updateService = (serviceType: string, packageId: string, updates: Partial<SelectedService>) => {
    setSelectedServices(prev => 
      prev.map(service => 
        service.serviceType === serviceType && service.packageId === packageId
          ? { ...service, ...updates }
          : service
      )
    );
  };

  const clearAllServices = () => {
    setSelectedServices([]);
    setFormDataStore({});
    localStorage.removeItem('selectedServices');
    localStorage.removeItem('serviceFormData');
  };

  const getServicesByType = (serviceType: string) => {
    return selectedServices.filter(service => service.serviceType === serviceType);
  };

  const hasService = (serviceType: string, packageId?: string) => {
    if (packageId) {
      return selectedServices.some(s => s.serviceType === serviceType && s.packageId === packageId);
    }
    return selectedServices.some(s => s.serviceType === serviceType);
  };

  const getFormData = (serviceType: string) => {
    return formDataStore[serviceType] || {};
  };

  const setFormData = (serviceType: string, formData: any) => {
    setFormDataStore(prev => ({
      ...prev,
      [serviceType]: formData
    }));
  };

  const contextValue: ServiceSelectionContextType = {
    selectedServices,
    totalAmount,
    addService,
    removeService,
    updateService,
    clearAllServices,
    getServicesByType,
    hasService,
    getFormData,
    setFormData
  };

  return (
    <ServiceSelectionContext.Provider value={contextValue}>
      {children}
    </ServiceSelectionContext.Provider>
  );
};
