# Email API Specification for Student Notifications

## Overview
This document specifies the email API endpoint needed to support student account creation notifications in the Zoffness Academy frontend.

## Required Endpoint

### POST /api/send-email

**Purpose:** Send email notifications for student account creation

**URL:** `https://zoffness.academy/api/send-email`

**Method:** POST

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**
```json
{
  "to": "<EMAIL>",
  "subject": "Your Zoffness Student Account Has Been Created",
  "html": "<div>HTML email content...</div>",
  "type": "student_account_notification"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Email sent successfully",
  "email_id": "unique_email_id"
}
```

**Error Responses:**

404 Not Found:
```json
{
  "success": false,
  "error": "Email service not available"
}
```

500 Internal Server Error:
```json
{
  "success": false,
  "error": "Email server error"
}
```

## Implementation Example (Laravel/PHP)

### Route (routes/api.php)
```php
Route::post('/send-email', [EmailController::class, 'sendEmail']);
```

### Controller (app/Http/Controllers/EmailController.php)
```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\StudentAccountNotification;

class EmailController extends Controller
{
    public function sendEmail(Request $request)
    {
        $request->validate([
            'to' => 'required|email',
            'subject' => 'required|string',
            'html' => 'required|string',
            'type' => 'required|string'
        ]);

        try {
            Mail::send([], [], function ($message) use ($request) {
                $message->to($request->to)
                        ->subject($request->subject)
                        ->html($request->html);
            });

            return response()->json([
                'success' => true,
                'message' => 'Email sent successfully',
                'email_id' => uniqid('email_')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to send email: ' . $e->getMessage()
            ], 500);
        }
    }
}
```

### Mail Configuration (.env)
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Zoffness Academy"
```

## Alternative Implementation (Node.js/Express)

### Route
```javascript
const express = require('express');
const nodemailer = require('nodemailer');
const router = express.Router();

// Configure nodemailer
const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
});

router.post('/send-email', async (req, res) => {
  const { to, subject, html, type } = req.body;

  try {
    await transporter.sendMail({
      from: '"Zoffness Academy" <<EMAIL>>',
      to: to,
      subject: subject,
      html: html
    });

    res.json({
      success: true,
      message: 'Email sent successfully',
      email_id: `email_${Date.now()}`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to send email: ' + error.message
    });
  }
});

module.exports = router;
```

## Testing the Endpoint

You can test the endpoint using curl:

```bash
curl -X POST https://zoffness.academy/api/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "html": "<h1>Test Email</h1><p>This is a test email.</p>",
    "type": "test"
  }'
```

## Current Frontend Integration

The frontend is already configured to use this endpoint. Once implemented, student account creation will automatically send email notifications with:

- Welcome message
- Login credentials
- Instructions for accessing the student dashboard
- Professional HTML formatting

## Security Considerations

1. **Rate Limiting:** Implement rate limiting to prevent email spam
2. **Validation:** Validate email addresses and content
3. **Authentication:** Consider requiring authentication for the endpoint
4. **Logging:** Log email sending attempts for debugging
5. **Error Handling:** Provide meaningful error messages without exposing sensitive information
